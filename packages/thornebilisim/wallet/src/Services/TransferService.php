<?php

namespace Thorne\Wallet\Services;

use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Thorne\Wallet\Enums\TransferStatus;
use Thorne\Wallet\Exceptions\WalletOperationException;
use Thorne\Wallet\Exceptions\WalletTransferException;
use Thorne\Wallet\Models\WalletAccount;
use Thorne\Wallet\Models\WalletTransfer;

class TransferService
{
    public function __construct(
        protected BalanceService $balanceService,
        protected LimitValidationService $limitValidationService
    ) {}

    public function createTransfer(array $data): WalletTransfer
    {
        $this->validateTransferData($data);

        $fromAccount = WalletAccount::findByAccountNumber($data['from_account_number']);
        $toAccount   = WalletAccount::findByAccountNumber($data['to_account_number']);

        if (! $fromAccount || ! $toAccount) {
            throw WalletOperationException::invalidAccountNumber(
                $fromAccount ? $data['to_account_number'] : $data['from_account_number'],
                'Account not found'
            );
        }

        if ($fromAccount->currency !== $toAccount->currency) {
            throw WalletOperationException::currencyMismatch($fromAccount->currency, $toAccount->currency);
        }

        $this->checkTransferPermissions($fromAccount->customer_id, $fromAccount->currency, $data['amount']);

        if (! $this->balanceService->hasSufficientBalance($fromAccount->customer_id, $fromAccount->currency, $data['amount'])) {
            $balance = $this->balanceService->getBalance($fromAccount->customer_id, $fromAccount->currency);
            throw WalletOperationException::insufficientBalance((float) $data['amount'], (float) $balance, $fromAccount->currency);
        }

        $fee         = $this->calculateTransferFee($data['amount'], $fromAccount->currency);
        $totalAmount = bcadd($data['amount'], $fee, 8);

        if (! $this->balanceService->hasSufficientBalance($fromAccount->customer_id, $fromAccount->currency, $totalAmount)) {
            $balance = $this->balanceService->getBalance($fromAccount->customer_id, $fromAccount->currency);
            throw WalletOperationException::insufficientBalance((float) $totalAmount, (float) $balance, $fromAccount->currency);
        }

        DB::beginTransaction();

        try {

            $this->balanceService->lockAmount($fromAccount->customer_id, $fromAccount->currency, $totalAmount);

            $transfer = WalletTransfer::createTransfer([
                'from_customer_id'    => $fromAccount->customer_id,
                'to_customer_id'      => $toAccount->customer_id,
                'from_account_number' => $data['from_account_number'],
                'to_account_number'   => $data['to_account_number'],
                'amount'              => $data['amount'],
                'fee'                 => $fee,
                'total_amount'        => $data['amount'],
                'description'         => $data['description'] ?? null,
                'metadata'            => array_merge($data['metadata'] ?? [], [
                    'fee_calculation' => [
                        'base_amount'    => $data['amount'],
                        'fee_amount'     => $fee,
                        'total_deducted' => $totalAmount,
                    ],
                ]),
            ]);

            DB::commit();

            if (wallet_config('logging.log_transactions', true)) {
                Log::channel(wallet_config('logging.channel', 'wallet'))
                    ->info('Transfer created', [
                        'transfer_id'      => $transfer->id,
                        'reference'        => $transfer->reference,
                        'from_customer_id' => $fromAccount->customer_id,
                        'to_customer_id'   => $toAccount->customer_id,
                        'amount'           => $data['amount'],
                        'fee'              => $fee,
                        'currency'         => $fromAccount->currency,
                    ]);
            }

            return $transfer;

        } catch (Exception $exception) {
            DB::rollBack();
            throw $exception;
        }
    }

    public function processTransfer(WalletTransfer $transfer, array $data = []): bool
    {
        if (! $transfer->isPending()) {
            throw WalletOperationException::transferNotPending($transfer->id, $transfer->status->value);
        }

        DB::beginTransaction();

        try {

            $transfer->complete($data);

            $totalAmount = bcadd($transfer->amount, $transfer->fee, 8);
            $this->balanceService->unlockAmount($transfer->from_customer_id, $transfer->currency, $totalAmount);

            if (wallet_config('web3.enabled', true)) {
                $this->balanceService->syncWithWeb3($transfer->from_customer_id, $transfer->currency);
                $this->balanceService->syncWithWeb3($transfer->to_customer_id, $transfer->currency);
            }

            DB::commit();

            if (wallet_config('logging.log_transactions', true)) {
                Log::channel(wallet_config('logging.channel', 'wallet'))
                    ->info('Transfer completed', [
                        'transfer_id'      => $transfer->id,
                        'reference'        => $transfer->reference,
                        'from_customer_id' => $transfer->from_customer_id,
                        'to_customer_id'   => $transfer->to_customer_id,
                        'amount'           => $transfer->amount,
                        'fee'              => $transfer->fee,
                        'currency'         => $transfer->currency,
                    ]);
            }

            return true;

        } catch (Exception $exception) {
            DB::rollBack();
            throw $exception;
        }
    }

    public function cancelTransfer(WalletTransfer $transfer, ?string $reason = null): bool
    {
        if (! $transfer->isPending()) {
            throw WalletOperationException::transferNotPending($transfer->id, $transfer->status->value);
        }

        DB::beginTransaction();

        try {

            $transfer->cancel($reason);

            $totalAmount = bcadd($transfer->amount, $transfer->fee, 8);
            $this->balanceService->unlockAmount($transfer->from_customer_id, $transfer->currency, $totalAmount);

            DB::commit();

            if (wallet_config('logging.log_transactions', true)) {
                Log::channel(wallet_config('logging.channel', 'wallet'))
                    ->info('Transfer cancelled', [
                        'transfer_id'      => $transfer->id,
                        'reference'        => $transfer->reference,
                        'reason'           => $reason,
                        'from_customer_id' => $transfer->from_customer_id,
                        'to_customer_id'   => $transfer->to_customer_id,
                        'amount'           => $transfer->amount,
                        'currency'         => $transfer->currency,
                    ]);
            }

            return true;

        } catch (Exception $exception) {
            DB::rollBack();
            throw $exception;
        }
    }

    public function getTransferHistory(int $customerId, array $filters = [])
    {
        $query = WalletTransfer::forCustomer($customerId)
            ->with(['fromCustomer', 'toCustomer', 'fromAccount', 'toAccount'])
            ->orderBy('created_at', 'desc');

        if (isset($filters['currency'])) {
            $query->forCurrency($filters['currency']);
        }

        if (isset($filters['status'])) {
            $query->withStatus(TransferStatus::from($filters['status']));
        }

        if (isset($filters['direction'])) {
            if ($filters['direction'] === 'outgoing') {
                $query->outgoingForCustomer($customerId);
            } elseif ($filters['direction'] === 'incoming') {
                $query->incomingForCustomer($customerId);
            }
        }

        if (isset($filters['from_date'])) {
            $query->where('created_at', '>=', $filters['from_date']);
        }

        if (isset($filters['to_date'])) {
            $query->where('created_at', '<=', $filters['to_date']);
        }

        return $query->paginate($filters['per_page'] ?? 15);
    }

    public function calculateTransferFee(string|float $amount, string $currency): string
    {

        return '0.********';
    }

    protected function validateTransferData(array $data): void
    {
        $required = ['from_account_number', 'to_account_number', 'amount'];

        foreach ($required as $field) {
            if (! isset($data[$field])) {
                throw WalletOperationException::missingRequiredField($field);
            }
        }

        if (strlen($data['from_account_number']) !== 11 || ! ctype_digit($data['from_account_number'])) {
            throw WalletOperationException::invalidAccountNumber($data['from_account_number'], 'Invalid format');
        }

        if (strlen($data['to_account_number']) !== 11 || ! ctype_digit($data['to_account_number'])) {
            throw WalletOperationException::invalidAccountNumber($data['to_account_number'], 'Invalid format');
        }

        if ($data['from_account_number'] === $data['to_account_number']) {
            throw WalletOperationException::sameAccountOperation($data['from_account_number']);
        }

        if (bccomp($data['amount'], '0', 8) <= 0) {
            throw WalletOperationException::invalidAmount((float) $data['amount']);
        }

        $luhnService = app(LuhnService::class);

        if (! $luhnService->validateAccountNumber($data['from_account_number'])) {
            throw WalletOperationException::invalidAccountNumber($data['from_account_number'], 'Luhn validation failed');
        }

        if (! $luhnService->validateAccountNumber($data['to_account_number'])) {
            throw WalletOperationException::invalidAccountNumber($data['to_account_number'], 'Luhn validation failed');
        }
    }

    protected function checkTransferPermissions(int $customerId, string $currency, string|float $amount): void
    {
        $this->limitValidationService->validateLimits('transfer', $customerId, (float) $amount, $currency);

        $permissions = wallet_config('permissions.transfer', []);

        if (! $permissions['enabled']) {
            throw WalletTransferException::transfersDisabled();
        }

        $minAmount = $permissions['min_amount'] ?? '0.********';
        if (bccomp($amount, $minAmount, 8) < 0) {
            throw WalletOperationException::amountBelowMinimum((float) $amount, (float) $minAmount, $currency);
        }

        $maxAmount = $permissions['max_amount'] ?? null;
        if ($maxAmount && bccomp($amount, $maxAmount, 8) > 0) {
            throw WalletOperationException::amountAboveMaximum((float) $amount, (float) $maxAmount, $currency);
        }

        $this->checkTransferLimits($customerId, $currency, $amount);
    }

    protected function checkTransferLimits(int $customerId, string $currency, string|float $amount): void
    {
        $permissions = wallet_config('permissions.transfer', []);

        if (isset($permissions['daily_limit']) && $permissions['daily_limit'] > 0) {
            $dailyUsage = $this->getDailyTransferUsage($customerId, $currency);
            if (($dailyUsage + (float) $amount) > $permissions['daily_limit']) {
                throw WalletTransferException::limitExceeded(
                    'Daily',
                    $permissions['daily_limit'],
                    $dailyUsage,
                    $currency
                );
            }
        }

        if (isset($permissions['monthly_limit']) && $permissions['monthly_limit'] > 0) {
            $monthlyUsage = $this->getMonthlyTransferUsage($customerId, $currency);
            if (($monthlyUsage + (float) $amount) > $permissions['monthly_limit']) {
                throw WalletTransferException::limitExceeded(
                    'Monthly',
                    $permissions['monthly_limit'],
                    $monthlyUsage,
                    $currency
                );
            }
        }
    }

    protected function getDailyTransferUsage(int $customerId, string $currency): float
    {
        return (float) WalletTransfer::where('from_customer_id', $customerId)
            ->where('currency', $currency)
            ->where('status', TransferStatus::COMPLETED)
            ->whereDate('created_at', today())
            ->sum('amount');
    }

    protected function getMonthlyTransferUsage(int $customerId, string $currency): float
    {
        return (float) WalletTransfer::where('from_customer_id', $customerId)
            ->where('currency', $currency)
            ->where('status', TransferStatus::COMPLETED)
            ->whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->sum('amount');
    }
}
