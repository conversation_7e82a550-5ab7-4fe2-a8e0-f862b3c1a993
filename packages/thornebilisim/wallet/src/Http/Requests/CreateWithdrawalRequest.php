<?php

namespace Thorne\Wallet\Http\Requests;

use Exception;
use Illuminate\Foundation\Http\FormRequest;
use Thorne\Wallet\Http\Requests\Traits\HasCustomerContext;
use Thorne\Wallet\Services\BalanceService;
use Thorne\Wallet\Services\LimitValidationService;

class CreateWithdrawalRequest extends FormRequest
{
    use HasCustomerContext;

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'method' => [
                'required',
                'string',
                function ($attribute, $value, $fail) {
                    $methodConfig = wallet_payment_method_config($value);
                    if (empty($methodConfig) || ! $methodConfig['enabled'] || ! $methodConfig['can_withdraw']) {
                        $fail(__('wallet::app.errors.method_not_supported'));
                    }
                },
            ],
            'currency' => [
                'required',
                'string',
                'between:3,4',
                function ($attribute, $value, $fail) {
                    if (! wallet_is_supported_currency(strtoupper($value))) {
                        $fail(__('wallet::app.errors.unsupported_currency'));
                    }
                },
            ],
            'amount' => [
                'required',
                'numeric',
                'min:0.01',
                function ($attribute, $value, $fail) {
                    $customer = $this->getCustomer();
                    $currency = strtoupper($this->input('currency'));

                    $balanceService = app(BalanceService::class);
                    if (! $balanceService->hasSufficientBalance($customer->id, $currency, $value)) {
                        $fail(__('wallet::app.errors.insufficient_balance'));

                        return;
                    }

                    try {
                        $limitService = app(LimitValidationService::class);
                        $limitService->validateAmountLimits(
                            'withdraw',
                            $customer->id,
                            (float) $value,
                            $currency
                        );
                    } catch (Exception $exception) {
                        $fail($exception->getMessage());
                    }
                },
            ],
            'account_details'                => 'required|array',
            'account_details.account_number' => 'required_if:method,bank_transfer|string',
            'account_details.bank_name'      => 'required_if:method,bank_transfer|string',
            'account_details.account_holder' => 'required_if:method,bank_transfer|string',
            'description'                    => 'nullable|string|max:255',
        ];
    }

    public function messages(): array
    {
        return [
            'method.required'             => __('wallet::app.withdrawals.select_method'),
            'currency.required'           => __('wallet::app.withdrawals.select_currency'),
            'currency.between'            => __('wallet::app.errors.currency_between'),
            'amount.required'             => __('wallet::app.withdrawals.enter_amount'),
            'amount.numeric'              => __('wallet::app.errors.invalid_amount'),
            'amount.min'                  => __('wallet::app.errors.invalid_amount'),
            'account_details.required'    => __('wallet::app.withdrawals.enter_account_details'),
        ];
    }
}
