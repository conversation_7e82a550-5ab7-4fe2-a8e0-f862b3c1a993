<?php

namespace Thorne\Wallet\Exceptions;

class WalletSystemException extends WalletException
{
    public static function invalidConfiguration(array $errors): self
    {
        return new self(
            'Wallet configuration is invalid: '.implode(', ', $errors),
            'INVALID_CONFIGURATION',
            ['errors' => $errors]
        );
    }

    public static function unsupportedHttpMethod(string $method): self
    {
        return new self(
            "Unsupported HTTP method: {$method}",
            'UNSUPPORTED_HTTP_METHOD',
            ['method' => $method]
        );
    }

    public static function web3ApiError(string $message, array $context = []): self
    {
        return new self(
            "WEB3 API Error: {$message}",
            'WEB3_API_ERROR',
            $context
        );
    }

    public static function web3SyncFailed(int $customerId, string $currency, string $reason): self
    {
        return new self(
            "WEB3 sync failed for customer {$customerId} ({$currency}): {$reason}",
            'WEB3_SYNC_FAILED',
            [
                'customer_id' => $customerId,
                'currency'    => $currency,
                'reason'      => $reason,
            ]
        );
    }

    public static function web3ConnectionFailed(string $endpoint, string $reason): self
    {
        return new self(
            "Failed to connect to WEB3 API at {$endpoint}: {$reason}",
            'WEB3_CONNECTION_FAILED',
            [
                'endpoint' => $endpoint,
                'reason'   => $reason,
            ]
        );
    }

    public static function web3ResponseInvalid(string $endpoint, string $reason): self
    {
        return new self(
            "Invalid response from WEB3 API at {$endpoint}: {$reason}",
            'WEB3_RESPONSE_INVALID',
            [
                'endpoint' => $endpoint,
                'reason'   => $reason,
            ]
        );
    }

    public static function databaseError(string $operation, string $reason): self
    {
        return new self(
            "Database error during {$operation}: {$reason}",
            'DATABASE_ERROR',
            [
                'operation' => $operation,
                'reason'    => $reason,
            ]
        );
    }

    public static function serviceUnavailable(string $service): self
    {
        return new self(
            "Service unavailable: {$service}",
            'SERVICE_UNAVAILABLE',
            ['service' => $service]
        );
    }

    public static function rateLimitExceeded(string $operation, int $limit, int $window): self
    {
        return new self(
            "Rate limit exceeded for {$operation}. Limit: {$limit} requests per {$window} seconds",
            'RATE_LIMIT_EXCEEDED',
            [
                'operation' => $operation,
                'limit'     => $limit,
                'window'    => $window,
            ]
        );
    }

    public static function maintenanceMode(): self
    {
        return new self(
            'Wallet system is currently under maintenance',
            'MAINTENANCE_MODE'
        );
    }
}
