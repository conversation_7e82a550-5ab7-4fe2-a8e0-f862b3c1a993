<?php

namespace Thorne\Wallet\Http\Controllers\Api;

use Exception;
use Illuminate\Http\JsonResponse;
use Thorne\Wallet\Http\Controllers\Controller;
use Thorne\Wallet\Http\Requests\Api\Wallet\CompareBalanceRequest;
use Thorne\Wallet\Http\Requests\Api\Wallet\CreateAccountRequest;
use Thorne\Wallet\Http\Requests\Api\Wallet\GetAccountsRequest;
use Thorne\Wallet\Http\Requests\Api\Wallet\GetBalancesRequest;
use Thorne\Wallet\Http\Requests\Api\Wallet\GetMethodsRequest;
use Thorne\Wallet\Http\Requests\Api\Wallet\GetSummaryRequest;
use Thorne\Wallet\Http\Requests\Api\Wallet\GetTransactionsRequest;
use Thorne\Wallet\Http\Requests\Api\Wallet\SyncBalanceRequest;
use Thorne\Wallet\Http\Requests\Api\Wallet\ValidateAccountRequest;
use Thorne\Wallet\Models\WalletAccount;
use Thorne\Wallet\Models\WalletTransaction;
use Thorne\Wallet\Services\BalanceService;
use Thorne\Wallet\Services\CustomerSessionService;
use Thorne\Wallet\Services\LuhnService;
use Thorne\Wallet\Services\WalletService;
use Illuminate\Http\Request;

class WalletController extends Controller
{
    public function __construct(
        protected WalletService $walletService,
        protected BalanceService $balanceService,
        protected LuhnService $luhnService,
        protected CustomerSessionService $customerSessionService
    ) {}

    public function currencies(): JsonResponse
    {
        $currencies = wallet_get_enabled_currencies();

        return response()->json([
            'success' => true,
            'data'    => $currencies,
        ]);
    }

    public function methods(GetMethodsRequest $request): JsonResponse
    {
        $operation = $request->input('operation');
        $currency  = $request->input('currency');

        if (! $operation) {
            return response()->json([
                'success' => false,
                'message' => 'Operation parameter is required (deposit or withdraw)',
            ], 400);
        }

        $methods   = wallet_config('payment_methods', []);
        $available = [];

        foreach ($methods as $methodKey => $methodConfig) {
            if (! $methodConfig['enabled']) {
                continue;
            }

            $canPerformOperation = match ($operation) {
                'deposit'  => $methodConfig['can_deposit'],
                'withdraw' => $methodConfig['can_withdraw'],
                default    => false,
            };

            if (! $canPerformOperation) {
                continue;
            }

            if ($currency && ! in_array($currency, $methodConfig['supported_currencies'])) {
                continue;
            }

            $available[$methodKey] = $methodConfig;
        }

        return response()->json([
            'success' => true,
            'data'    => $available,
        ]);
    }

    public function validateAccount(ValidateAccountRequest $request): JsonResponse
    {
        $accountNumber = $request->input('account_number');

        if (! $this->luhnService->validateAccountNumber($accountNumber)) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid account number format',
                'data'    => [
                    'is_valid' => false,
                    'reason'   => 'luhn_validation_failed',
                ],
            ]);
        }

        $account = WalletAccount::findByAccountNumber($accountNumber);
        if (! $account) {
            return response()->json([
                'success' => false,
                'message' => 'Account not found',
                'data'    => [
                    'is_valid' => false,
                    'reason'   => 'account_not_found',
                ],
            ]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Account is valid',
            'data'    => [
                'is_valid'                 => true,
                'account_number'           => $account->account_number,
                'formatted_account_number' => $account->getFormattedAccountNumber(),
                'currency'                 => $account->currency,
                'is_active'                => $account->is_active,
            ],
        ]);
    }

    public function summary(Request $request): JsonResponse
    {
        $customer     = $this->customerSessionService->getCustomerOrFail();
        $syncWithWeb3 = $request->boolean('sync', false);

        $summary = $this->walletService->getWalletSummary($customer, $syncWithWeb3);

        return response()->json([
            'success' => true,
            'data'    => $summary,
        ]);
    }

    public function showBalance(Request $request): JsonResponse
    {
        $customer  = $this->customerSessionService->getCustomerOrFail();
        $currency  = $request->input('currency');
        $forceSync = $request->boolean('sync', false);

        if ($currency) {
            if (! wallet_is_supported_currency($currency)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unsupported currency',
                ], 400);
            }

            $balance = $this->balanceService->getBalance($customer->id, $currency, $forceSync);

            return response()->json([
                'success' => true,
                'data'    => [
                    'currency'                    => $balance->currency,
                    'balance'                     => $balance->balance,
                    'locked_balance'              => $balance->locked_balance,
                    'available_balance'           => $balance->getAvailableBalance(),
                    'formatted_balance'           => $balance->getFormattedBalance(),
                    'formatted_available_balance' => $balance->getFormattedAvailableBalance(),
                    'formatted_locked_balance'    => $balance->getFormattedLockedBalance(),
                    'last_sync'                   => $balance->web3_synced_at?->toISOString(),
                    'web3_data'                   => [
                        'address'         => $balance->getWeb3Address(),
                        'chain_id'        => $balance->getWeb3ChainId(),
                        'raw_balance'     => $balance->getRawWeb3Balance(),
                        'pending_balance' => $balance->getWeb3PendingBalance(),
                        'exponent'        => $balance->getCurrencyExponent(),
                        'is_stable_coin'  => $balance->isStableCoin(),
                        'is_cbdc'         => $balance->isCbdc(),
                    ],
                    'sync_status' => $balance->getWeb3SyncStatus(),
                ],
            ]);
        }

        $balances = $this->walletService->getCustomerBalances($customer->id, $forceSync);

        return response()->json([
            'success' => true,
            'data'    => $balances,
        ]);
    }

    public function syncBalance(SyncBalanceRequest $request): JsonResponse
    {
        $customer = $this->customerSessionService->getCustomerOrFail();
        $currency = strtoupper($request->input('currency'));

        try {
            $balance = $this->balanceService->syncWithWeb3($customer->id, $currency);

            return response()->json([
                'success' => true,
                'data'    => [
                    'currency'                    => $balance->currency,
                    'balance'                     => $balance->balance,
                    'locked_balance'              => $balance->locked_balance,
                    'available_balance'           => $balance->getAvailableBalance(),
                    'formatted_balance'           => $balance->getFormattedBalance(),
                    'formatted_available_balance' => $balance->getFormattedAvailableBalance(),
                    'formatted_locked_balance'    => $balance->getFormattedLockedBalance(),
                    'last_sync'                   => $balance->web3_synced_at?->toISOString(),
                    'web3_data'                   => [
                        'address'         => $balance->getWeb3Address(),
                        'chain_id'        => $balance->getWeb3ChainId(),
                        'raw_balance'     => $balance->getRawWeb3Balance(),
                        'pending_balance' => $balance->getWeb3PendingBalance(),
                        'exponent'        => $balance->getCurrencyExponent(),
                        'is_stable_coin'  => $balance->isStableCoin(),
                        'is_cbdc'         => $balance->isCbdc(),
                    ],
                    'sync_status' => $balance->getWeb3SyncStatus(),
                ],
            ]);

        } catch (Exception $exception) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to sync balance: '.$exception->getMessage(),
            ], 500);
        }
    }

    public function syncAllBalances(): JsonResponse
    {
        $customer = $this->customerSessionService->getCustomerOrFail();

        try {
            $balances = $this->balanceService->syncAllWithWeb3($customer->id);
            $result   = $this->walletService->getCustomerBalances($customer->id, false);

            return response()->json([
                'success' => true,
                'message' => 'All balances synced successfully',
                'data'    => $result,
            ]);

        } catch (Exception $exception) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to sync balances: '.$exception->getMessage(),
            ], 500);
        }
    }

    public function compareBalance(CompareBalanceRequest $request): JsonResponse
    {
        $customer = $this->customerSessionService->getCustomerOrFail();

        try {
            $comparison = $this->balanceService->compareBalances($customer->id, $request->input('currency'));

            return response()->json([
                'success' => true,
                'data'    => $comparison,
            ]);

        } catch (Exception $exception) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to compare balances: '.$exception->getMessage(),
            ], 500);
        }
    }

    public function indexAccounts(): JsonResponse
    {
        $customer = $this->customerSessionService->getCustomerOrFail();
        $accounts = $customer->walletAccounts()->active()->get();

        return response()->json([
            'success' => true,
            'data'    => $accounts->map(function ($account) {
                return [
                    'id'                       => $account->id,
                    'currency'                 => $account->currency,
                    'account_number'           => $account->account_number,
                    'formatted_account_number' => $account->getFormattedAccountNumber(),
                    'is_active'                => $account->is_active,
                    'created_at'               => $account->created_at->toISOString(),
                ];
            }),
        ]);
    }

    public function storeAccount(CreateAccountRequest $request): JsonResponse
    {
        $customer = $this->customerSessionService->getCustomerOrFail();
        $currency = strtoupper($request->input('currency'));

        $existingAccount = WalletAccount::where('customer_id', $customer->id)
            ->where('currency', $currency)
            ->first();

        if ($existingAccount) {
            return response()->json([
                'success' => false,
                'message' => 'Account already exists for this currency',
            ], 400);
        }

        try {
            $account = WalletAccount::getOrCreateForCustomer($customer->id, $currency);

            return response()->json([
                'success' => true,
                'data'    => [
                    'id'                       => $account->id,
                    'currency'                 => $account->currency,
                    'account_number'           => $account->account_number,
                    'formatted_account_number' => $account->getFormattedAccountNumber(),
                    'is_active'                => $account->is_active,
                    'created_at'               => $account->created_at->toISOString(),
                ],
            ]);

        } catch (Exception $exception) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create account: '.$exception->getMessage(),
            ], 500);
        }
    }

    public function indexTransactions(Request $request): JsonResponse
    {
        $customer = $this->customerSessionService->getCustomerOrFail();

        $filters = [
            'currency'  => $request->input('currency'),
            'type'      => $request->input('type'),
            'status'    => $request->input('status'),
            'method'    => $request->input('method'),
            'from_date' => $request->input('from_date'),
            'to_date'   => $request->input('to_date'),
            'per_page'  => $request->input('per_page', 15),
        ];

        $transactions = $this->walletService->getTransactionHistory($customer->id, $filters);

        return response()->json([
            'success'    => true,
            'data'       => $transactions->items(),
            'pagination' => [
                'current_page' => $transactions->currentPage(),
                'last_page'    => $transactions->lastPage(),
                'per_page'     => $transactions->perPage(),
                'total'        => $transactions->total(),
                'from'         => $transactions->firstItem(),
                'to'           => $transactions->lastItem(),
            ],
        ]);
    }

    public function showTransaction(WalletTransaction $transaction): JsonResponse
    {
        $customer = $this->customerSessionService->getCustomerOrFail();

        if ($transaction->customer_id !== $customer->id) {
            return response()->json([
                'success' => false,
                'message' => 'Transaction not found',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data'    => [
                'id'                      => $transaction->id,
                'reference'               => $transaction->reference,
                'type'                    => $transaction->type->value,
                'direction'               => $transaction->direction->value,
                'status'                  => $transaction->status->value,
                'method'                  => $transaction->method,
                'currency'                => $transaction->currency,
                'amount'                  => $transaction->amount,
                'fee'                     => $transaction->fee,
                'total_amount'            => $transaction->total_amount,
                'formatted_amount'        => $transaction->getFormattedAmount(),
                'formatted_fee'           => $transaction->getFormattedFee(),
                'formatted_total_amount'  => $transaction->getFormattedTotalAmount(),
                'description'             => $transaction->description,
                'metadata'                => $transaction->metadata,
                'external_reference'      => $transaction->external_reference,
                'external_transaction_id' => $transaction->external_transaction_id,
                'processed_at'            => $transaction->processed_at?->toISOString(),
                'failed_at'               => $transaction->failed_at?->toISOString(),
                'failure_reason'          => $transaction->failure_reason,
                'created_at'              => $transaction->created_at->toISOString(),
                'updated_at'              => $transaction->updated_at->toISOString(),
                'age'                     => $transaction->getAge(),
            ],
        ]);
    }
}
