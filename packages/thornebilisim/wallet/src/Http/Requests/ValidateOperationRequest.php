<?php

namespace Thorne\Wallet\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ValidateOperationRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $enabledCurrencies = array_keys(wallet_get_enabled_currencies());

        return [
            'operation' => 'required|string|in:deposit,withdraw,transfer',
            'amount'    => 'required|numeric|min:0.01',
            'currency'  => [
                'required',
                'string',
                'between:3,4',
                'in:'.implode(',', $enabledCurrencies),
                function ($attribute, $value, $fail) {
                    if (! wallet_is_supported_currency(strtoupper($value))) {
                        $fail(__('wallet::app.errors.unsupported_currency'));
                    }
                },
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'operation.required'     => __('wallet::app.errors.operation_required'),
            'operation.string'       => __('wallet::app.errors.operation_string'),
            'operation.in'           => __('wallet::app.errors.operation_not_supported'),
            'amount.required'        => __('wallet::app.errors.amount_required'),
            'amount.numeric'         => __('wallet::app.errors.amount_numeric'),
            'amount.min'             => __('wallet::app.errors.amount_min'),
            'currency.required'      => __('wallet::app.errors.currency_required'),
            'currency.string'        => __('wallet::app.errors.currency_string'),
            'currency.between'       => __('wallet::app.errors.currency_between'),
            'currency.in'            => __('wallet::app.errors.currency_not_supported'),
        ];
    }
}
