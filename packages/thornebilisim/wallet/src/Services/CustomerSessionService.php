<?php

namespace Thorne\Wallet\Services;

use Thorne\Wallet\Exceptions\CustomerNotFoundException;
use Webkul\Customer\Models\Customer;

class CustomerSessionService
{
    public function resolve(): ?Customer
    {
        $customerId = request()->secureContext('customer_id');

        if ($customerId) {
            return Customer::find($customerId);
        }

        return null;
    }

    public function getCustomerOrFail(): Customer
    {
        $customer = $this->resolve();

        if (!$customer) {
            throw new CustomerNotFoundException();
        }

        return $customer;
    }
}
