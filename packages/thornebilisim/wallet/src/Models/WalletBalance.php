<?php

namespace Thorne\Wallet\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Contracts\Auditable;
use Thorne\Wallet\Exceptions\WalletOperationException;
use Thorne\Wallet\Models\Traits\HasWalletAudit;
use Webkul\Customer\Models\Customer;

class WalletBalance extends Model implements Auditable
{
    use HasFactory, HasWalletAudit, SoftDeletes;

    protected $table = 'wallet_balances';

    protected $fillable = [
        'customer_id',
        'currency',
        'balance',
        'locked_balance',
        'web3_synced_at',
        'web3_data',
        'created_at',
        'updated_at',
    ];

    protected $casts = [
        'balance'        => 'decimal:8',
        'locked_balance' => 'decimal:8',
        'web3_synced_at' => 'datetime',
        'web3_data'      => 'array',
        'created_at'     => 'datetime',
        'updated_at'     => 'datetime',
    ];

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function account(): BelongsTo
    {
        return $this->belongsTo(WalletAccount::class, 'customer_id', 'customer_id')
            ->where('currency', $this->currency);
    }

    public function getAvailableBalance(): string
    {
        return bcsub($this->balance, $this->locked_balance, 8);
    }

    public function getFormattedBalance(): string
    {
        return wallet_format_amount($this->balance, $this->currency);
    }

    public function getFormattedAvailableBalance(): string
    {
        return wallet_format_amount($this->getAvailableBalance(), $this->currency);
    }

    public function getFormattedLockedBalance(): string
    {
        return wallet_format_amount($this->locked_balance, $this->currency);
    }

    public function hasSufficientBalance(string|float $amount, bool $includeLockedBalance = false): bool
    {
        $availableBalance = $includeLockedBalance ? $this->balance : $this->getAvailableBalance();

        return bccomp($availableBalance, (string) $amount, 8) >= 0;
    }

    public function lockAmount(string|float $amount): bool
    {
        $amount = (string) $amount;

        if (bccomp($amount, '0', 8) <= 0) {
            throw WalletOperationException::lockAmountInvalid((float) $amount);
        }

        if (! $this->hasSufficientBalance($amount)) {
            throw WalletOperationException::insufficientAvailableBalance((float) $amount, (float) $this->available_balance);
        }

        $newLockedBalance = bcadd($this->locked_balance, $amount, 8);

        return $this->update(['locked_balance' => $newLockedBalance]);
    }

    public function unlockAmount(string|float $amount): bool
    {
        $amount = (string) $amount;

        if (bccomp($amount, '0', 8) <= 0) {
            throw WalletOperationException::unlockAmountInvalid((float) $amount);
        }

        if (bccomp($this->locked_balance, $amount, 8) < 0) {
            throw WalletOperationException::insufficientLockedBalance((float) $amount, (float) $this->locked_balance);
        }

        $newLockedBalance = bcsub($this->locked_balance, $amount, 8);

        return $this->update(['locked_balance' => $newLockedBalance]);
    }

    public function updateFromWeb3(array $web3Data): bool
    {
        $balance       = $web3Data['balance']        ?? '0';
        $lockedBalance = $web3Data['locked_balance'] ?? $this->locked_balance;

        return $this->update([
            'balance'        => $balance,
            'locked_balance' => $lockedBalance,
            'web3_synced_at' => now(),
            'web3_data'      => $web3Data,
        ]);
    }

    public function needsWeb3Sync(int $maxAgeMinutes = 30): bool
    {
        if ($this->web3_synced_at === null) {
            return true;
        }

        return $this->web3_synced_at->diffInMinutes(now()) > $maxAgeMinutes;
    }

    public static function getOrCreateForCustomer(int $customerId, string $currency): static
    {
        return static::firstOrCreate(
            [
                'customer_id' => $customerId,
                'currency'    => $currency,
            ],
            [
                'balance'        => '0.00000000',
                'locked_balance' => '0.00000000',
                'web3_data'      => [],
            ]
        );
    }

    public static function getCustomerBalances(int $customerId)
    {
        return static::where('customer_id', $customerId)->get();
    }

    public static function getCustomerBalance(int $customerId, string $currency): ?static
    {
        return static::where('customer_id', $customerId)
            ->where('currency', $currency)
            ->first();
    }

    public function scopeForCurrency($query, string $currency)
    {
        return $query->where('currency', $currency);
    }

    public function scopeNeedsSync($query, int $maxAgeMinutes = 30)
    {
        return $query->where(function ($q) use ($maxAgeMinutes) {
            $q->whereNull('web3_synced_at')
                ->orWhere('web3_synced_at', '<', now()->subMinutes($maxAgeMinutes));
        });
    }

    public function scopeWithBalance($query)
    {
        return $query->where('balance', '>', '0');
    }

    public function getWeb3Address(): ?string
    {
        return $this->web3_data['address'] ?? null;
    }

    public function getWeb3ChainId(): ?string
    {
        return $this->web3_data['chain_id'] ?? null;
    }

    public function getRawWeb3Balance(): ?int
    {
        return $this->web3_data['raw_balance'] ?? null;
    }

    public function getWeb3PendingBalance(): ?int
    {
        return $this->web3_data['pending_balance'] ?? null;
    }

    public function getCurrencyExponent(): int
    {
        return $this->web3_data['exponent'] ?? 0;
    }

    public function isStableCoin(): bool
    {
        return $this->web3_data['is_stable_coin'] ?? false;
    }

    public function isCbdc(): bool
    {
        return $this->web3_data['is_cbdc'] ?? false;
    }

    public function getWeb3SyncStatus(): array
    {
        return [
            'last_sync'        => $this->web3_synced_at?->toISOString(),
            'is_synced'        => $this->web3_synced_at !== null,
            'sync_age_minutes' => $this->web3_synced_at ? $this->web3_synced_at->diffInMinutes(now()) : null,
            'needs_sync'       => $this->web3_synced_at === null || $this->web3_synced_at->lt(now()->subMinutes(wallet_config('web3.cache_ttl', 30))),
        ];
    }
}
