<?php

namespace Thorne\Wallet\Http\Controllers\Api;

use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Thorne\Wallet\Enums\TransactionType;
use <PERSON>\Wallet\Exceptions\WalletOperationException;
use Thorne\Wallet\Http\Controllers\Controller;
use Thorne\Wallet\Http\Requests\CreateDepositRequest;
use Thorne\Wallet\Models\WalletTransaction;
use Thorne\Wallet\Services\CompanyAccountService;
use Thorne\Wallet\Services\CustomerSessionService;
use Thorne\Wallet\Services\LimitValidationService;
use Thorne\Wallet\Services\PermissionService;
use Thorne\Wallet\Services\WalletService;

class DepositController extends Controller
{
    public function __construct(
        protected WalletService $walletService,
        protected CompanyAccountService $companyAccountService,
        protected PermissionService $permissionService,
        protected CustomerSessionService $customerSessionService,
        protected LimitValidationService $limitValidationService
    ) {}

    public function index(Request $request): JsonResponse
    {
        $customer = $this->customerSessionService->getCustomerOrFail();

        $filters = [
            'currency'  => $request->input('currency'),
            'status'    => $request->input('status'),
            'method'    => $request->input('method'),
            'from_date' => $request->input('from_date'),
            'to_date'   => $request->input('to_date'),
            'per_page'  => $request->input('per_page', 15),
        ];

        $filters['type'] = TransactionType::DEPOSIT;

        $deposits = $this->walletService->getTransactionHistory($customer->id, $filters);

        return response()->json([
            'success'    => true,
            'data'       => $deposits->items(),
            'pagination' => [
                'current_page' => $deposits->currentPage(),
                'last_page'    => $deposits->lastPage(),
                'per_page'     => $deposits->perPage(),
                'total'        => $deposits->total(),
                'from'         => $deposits->firstItem(),
                'to'           => $deposits->lastItem(),
            ],
        ]);
    }

    public function store(CreateDepositRequest $request): JsonResponse
    {
        $customer = $this->customerSessionService->getCustomerOrFail();
        $currency = strtoupper($request->input('currency'));

        try {
            $this->limitValidationService->validateLimits(
                'deposit',
                $customer->id,
                (float) $request->input('amount'),
                $currency,
                $request->input('method')
            );

            $account = null;
            if ($request->input('account_id')) {
                $account = $this->companyAccountService->getAccountById($request->input('account_id'));
                if (! $account) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Invalid account ID',
                    ], 400);
                }
            } else {
                $account = $this->companyAccountService->getAccountForDeposit(
                    $request->input('method'),
                    $currency,
                    $request->input('amount')
                );
            }

            if (! $account) {
                return response()->json([
                    'success' => false,
                    'message' => 'No available account for this deposit method and currency',
                ], 400);
            }

            $deposit = $this->walletService->createDeposit([
                'customer_id'  => $customer->id,
                'method'       => $request->input('method'),
                'currency'     => $currency,
                'amount'       => $request->input('amount'),
                'description'  => $request->input('description'),
                'account_info' => $account,
                'metadata'     => [
                    'ip_address'   => $request->ip(),
                    'user_agent'   => $request->userAgent(),
                    'account_id'   => $account['id'],
                    'account_name' => $account['name'],
                ],
            ]);

            $this->permissionService->logPermissionCheck(
                'deposit',
                $customer->id,
                $request->input('amount'),
                $currency,
                true
            );

            return response()->json([
                'success' => true,
                'message' => 'Deposit request created successfully',
                'data'    => [
                    'id'               => $deposit->id,
                    'reference'        => $deposit->reference,
                    'type'             => $deposit->type->value,
                    'status'           => $deposit->status->value,
                    'method'           => $deposit->method,
                    'currency'         => $deposit->currency,
                    'amount'           => $deposit->amount,
                    'formatted_amount' => $deposit->getFormattedAmount(),
                    'description'      => $deposit->description,
                    'created_at'       => $deposit->created_at->toISOString(),
                    'account_info'     => $account,
                ],
            ]);

        } catch (WalletOperationException $exception) {
            $this->permissionService->logPermissionCheck(
                'deposit',
                $customer->id,
                $request->input('amount'),
                $currency,
                false,
                $exception->getErrors()
            );

            return response()->json([
                'success' => false,
                'message' => $exception->getMessage(),
                'errors'  => $exception->getErrors(),
                'context' => $exception->getContext(),
            ], 403);

        } catch (Exception $exception) {
            return response()->json([
                'success' => false,
                'message' => $exception->getMessage(),
            ], 400);
        }
    }

    public function methods(Request $request): JsonResponse
    {
        $customer = $this->customerSessionService->getCustomerOrFail();
        $currency = $request->input('currency');

        $methods = $this->walletService->getAvailablePaymentMethods($customer->id, 'deposit', $currency);

        return response()->json([
            'success' => true,
            'data'    => $methods,
        ]);
    }

    public function companyAccounts(Request $request): JsonResponse
    {
        $method    = $request->input('method');
        $currency  = $request->input('currency');
        $accountId = $request->input('account_id');

        try {
            if ($accountId) {
                $account  = $this->companyAccountService->getAccountById($accountId);
                $accounts = $account ? [$account] : [];
            } elseif ($method && $currency) {
                $accounts = $this->companyAccountService->getAccountsByMethodAndCurrency($method, $currency);
            } elseif ($method) {
                $accounts = $this->companyAccountService->getActiveAccountsByMethod($method);
            } else {
                $accounts = $this->companyAccountService->getAllAccounts();
            }

            return response()->json([
                'success' => true,
                'data'    => $accounts,
                'meta'    => [
                    'total_accounts'       => count($accounts),
                    'method'               => $method,
                    'currency'             => $currency,
                    'account_id'           => $accountId,
                    'supported_currencies' => $method ? $this->companyAccountService->getSupportedCurrencies($method) : [],
                ],
            ]);

        } catch (Exception $exception) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve company accounts: '.$exception->getMessage(),
            ], 500);
        }
    }

    public function show(WalletTransaction $transaction): JsonResponse
    {
        $customer = $this->customerSessionService->getCustomerOrFail();

        if ($transaction->customer_id !== $customer->id || $transaction->type !== TransactionType::DEPOSIT) {
            return response()->json([
                'success' => false,
                'message' => 'Deposit not found',
            ], 404);
        }

        $companyAccounts = wallet_config('company_accounts', []);
        $accounts        = $companyAccounts[$transaction->method][$transaction->currency] ?? [];

        return response()->json([
            'success' => true,
            'data'    => [
                'transaction' => [
                    'id'                      => $transaction->id,
                    'reference'               => $transaction->reference,
                    'type'                    => $transaction->type->value,
                    'status'                  => $transaction->status->value,
                    'method'                  => $transaction->method,
                    'currency'                => $transaction->currency,
                    'amount'                  => $transaction->amount,
                    'fee'                     => $transaction->fee,
                    'total_amount'            => $transaction->total_amount,
                    'formatted_amount'        => $transaction->getFormattedAmount(),
                    'formatted_fee'           => $transaction->getFormattedFee(),
                    'formatted_total_amount'  => $transaction->getFormattedTotalAmount(),
                    'description'             => $transaction->description,
                    'metadata'                => $transaction->metadata,
                    'external_reference'      => $transaction->external_reference,
                    'external_transaction_id' => $transaction->external_transaction_id,
                    'processed_at'            => $transaction->processed_at?->toISOString(),
                    'failed_at'               => $transaction->failed_at?->toISOString(),
                    'failure_reason'          => $transaction->failure_reason,
                    'created_at'              => $transaction->created_at->toISOString(),
                    'updated_at'              => $transaction->updated_at->toISOString(),
                    'age'                     => $transaction->getAge(),
                ],
                'company_accounts' => $accounts,
            ],
        ]);
    }

    public function showAccount(string $accountId): JsonResponse
    {
        try {
            $account = $this->companyAccountService->getAccountById($accountId);

            if (! $account) {
                return response()->json([
                    'success' => false,
                    'message' => 'Account not found',
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data'    => $account,
            ]);

        } catch (Exception $exception) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve account: '.$exception->getMessage(),
            ], 500);
        }
    }

    public function accountsByMethod(string $method): JsonResponse
    {
        try {
            $accounts        = $this->companyAccountService->getActiveAccountsByMethod($method);
            $groupedAccounts = $this->companyAccountService->getAccountsGroupedByCurrency($method);

            return response()->json([
                'success'             => true,
                'data'                => $accounts,
                'grouped_by_currency' => $groupedAccounts,
                'meta'                => [
                    'method'               => $method,
                    'total_accounts'       => count($accounts),
                    'supported_currencies' => $this->companyAccountService->getSupportedCurrencies($method),
                    'statistics'           => $this->companyAccountService->getAccountStatistics(),
                ],
            ]);

        } catch (Exception $exception) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve accounts: '.$exception->getMessage(),
            ], 500);
        }
    }

    public function accountsByMethodCurrency(string $method, string $currency): JsonResponse
    {
        try {
            $accounts       = $this->companyAccountService->getAccountsByMethodAndCurrency($method, $currency);
            $primaryAccount = $this->companyAccountService->getPrimaryAccount($method, $currency);

            return response()->json([
                'success'         => true,
                'data'            => $accounts,
                'primary_account' => $primaryAccount,
                'meta'            => [
                    'method'         => $method,
                    'currency'       => $currency,
                    'total_accounts' => count($accounts),
                    'has_primary'    => $primaryAccount !== null,
                ],
            ]);

        } catch (Exception $exception) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve accounts: '.$exception->getMessage(),
            ], 500);
        }
    }
}
