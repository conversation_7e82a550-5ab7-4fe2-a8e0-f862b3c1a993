<?php

namespace Thorne\Wallet\Http\Controllers\Api;

use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Thorne\Wallet\Exceptions\WalletOperationException;
use Thorne\Wallet\Http\Controllers\Controller;
use Thorne\Wallet\Http\Requests\CheckOperationPermissionRequest;
use Thorne\Wallet\Http\Requests\ValidateOperationRequest;
use Thorne\Wallet\Services\CustomerSessionService;
use Thorne\Wallet\Services\LimitValidationService;
use Thorne\Wallet\Services\PermissionService;

class PermissionController extends Controller
{
    public function __construct(
        protected PermissionService $permissionService,
        protected CustomerSessionService $customerSessionService,
        protected LimitValidationService $limitValidationService
    ) {}

    public function showLimit(string $operation): JsonResponse
    {
        try {
            if (! in_array($operation, ['deposit', 'withdraw', 'transfer'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid operation. Must be one of: deposit, withdraw, transfer',
                ], 400);
            }

            $limits = $this->limitValidationService->getOperationLimits($operation);

            return response()->json([
                'success'   => true,
                'data'      => $limits,
                'operation' => $operation,
            ]);

        } catch (Exception $exception) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve operation limits: '.$exception->getMessage(),
            ], 500);
        }
    }

    public function limits(): JsonResponse
    {
        try {
            $operations = ['deposit', 'withdraw', 'transfer'];
            $allLimits  = [];

            foreach ($operations as $operation) {
                $allLimits[$operation] = $this->limitValidationService->getOperationLimits($operation);
            }

            return response()->json([
                'success'    => true,
                'data'       => $allLimits,
                'operations' => $operations,
            ]);

        } catch (Exception $exception) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve operation limits: '.$exception->getMessage(),
            ], 500);
        }
    }

    public function usageStatistics(Request $request): JsonResponse
    {
        $customer = $this->customerSessionService->getCustomerOrFail();
        $currency = $request->input('currency');

        try {
            if ($currency && $currency !== 'all') {
                $stats = $this->limitValidationService->getUsageStatistics($customer->id, $currency);
            } else {
                $stats = $this->limitValidationService->getUsageStatisticsForAllCurrencies($customer->id);
            }

            return response()->json([
                'success'     => true,
                'data'        => $stats,
                'customer_id' => $customer->id,
                'currency'    => $currency ?? 'all',
                'meta'        => [
                    'timestamp' => now()->toISOString(),
                    'timezone'  => config('app.timezone'),
                ],
            ]);

        } catch (Exception $exception) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve usage statistics: '.$exception->getMessage(),
            ], 500);
        }
    }

    public function checkOperationPermission(CheckOperationPermissionRequest $request): JsonResponse
    {
        $customer = $this->customerSessionService->getCustomerOrFail();

        try {
            $errors = $this->limitValidationService->validateOperation(
                $request->input('operation'),
                $customer->id,
                $request->input('amount'),
                $request->input('currency'),
                $request->input('method')
            );

            $allowed         = empty($errors);
            $remainingLimits = $this->limitValidationService->getRemainingLimits(
                $customer->id,
                $request->input('operation'),
                $request->input('currency')
            );

            $this->permissionService->logPermissionCheck(
                $request->input('operation'),
                $customer->id,
                $request->input('amount'),
                $request->input('currency'),
                $allowed,
                $errors
            );

            return response()->json([
                'success' => true,
                'data'    => [
                    'allowed'          => $allowed,
                    'errors'           => $errors,
                    'remaining_limits' => $remainingLimits,
                    'operation_limits' => $this->limitValidationService->getOperationLimits($request->input('operation')),
                    'usage_statistics' => $this->limitValidationService->getUsageStatistics($customer->id, $request->input('currency'))[$request->input('operation')] ?? null,
                ],
                'operation'   => $request->input('operation'),
                'amount'      => $request->input('amount'),
                'currency'    => $request->input('currency'),
                'customer_id' => $customer->id,
                'meta'        => [
                    'timestamp'  => now()->toISOString(),
                    'check_type' => 'comprehensive',
                ],
            ]);

        } catch (Exception $exception) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to check operation permission: '.$exception->getMessage(),
            ], 500);
        }
    }

    public function validateOperation(ValidateOperationRequest $request): JsonResponse
    {
        $customer = $this->customerSessionService->getCustomerOrFail();

        try {
            $errors = $this->limitValidationService->validateOperation(
                $request->input('operation'),
                $customer->id,
                $request->input('amount'),
                $request->input('currency'),
                $request->input('method')
            );

            $allowed = empty($errors);

            if ($allowed) {
                return response()->json([
                    'success' => true,
                    'message' => 'Operation is allowed',
                    'data'    => [
                        'allowed'   => true,
                        'operation' => $request->input('operation'),
                        'amount'    => $request->input('amount'),
                        'currency'  => $request->input('currency'),
                    ],
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Operation not allowed',
                    'data'    => [
                        'allowed'   => false,
                        'errors'    => $errors,
                        'operation' => $request->input('operation'),
                        'amount'    => $request->input('amount'),
                        'currency'  => $request->input('currency'),
                    ],
                ], 403);
            }

        } catch (WalletOperationException $exception) {
            return response()->json([
                'success' => false,
                'message' => $exception->getMessage(),
                'data'    => [
                    'allowed' => false,
                    'errors'  => $exception->getErrors(),
                    'context' => $exception->getContext(),
                ],
            ], 403);

        } catch (Exception $exception) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to validate operation: '.$exception->getMessage(),
            ], 500);
        }
    }

    public function summary(): JsonResponse
    {
        try {
            $summary = $this->limitValidationService->getOperationSummary();

            return response()->json([
                'success'    => true,
                'data'       => $summary,
                'operations' => array_keys($summary),
                'meta'       => [
                    'timestamp'     => now()->toISOString(),
                    'config_source' => 'wallet.permissions',
                ],
            ]);

        } catch (Exception $exception) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve permission summary: '.$exception->getMessage(),
            ], 500);
        }
    }
}
