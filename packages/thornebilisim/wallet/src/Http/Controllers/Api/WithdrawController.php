<?php

namespace Thorne\Wallet\Http\Controllers\Api;

use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Thorne\Wallet\Enums\TransactionType;
use Thorne\Wallet\Exceptions\WalletOperationException;
use Thorne\Wallet\Http\Controllers\Controller;
use Thorne\Wallet\Http\Requests\CreateWithdrawalRequest;
use Thorne\Wallet\Models\WalletTransaction;
use Thorne\Wallet\Services\BalanceService;
use Thorne\Wallet\Services\CustomerSessionService;
use Thorne\Wallet\Services\LimitValidationService;
use Thorne\Wallet\Services\PermissionService;
use Thorne\Wallet\Services\WalletService;

class WithdrawController extends Controller
{
    public function __construct(
        protected WalletService $walletService,
        protected BalanceService $balanceService,
        protected PermissionService $permissionService,
        protected CustomerSessionService $customerSessionService,
        protected LimitValidationService $limitValidationService
    ) {}

    public function index(Request $request): JsonResponse
    {
        $customer = $this->customerSessionService->getCustomerOrFail();

        $filters = [
            'currency'  => $request->input('currency'),
            'status'    => $request->input('status'),
            'method'    => $request->input('method'),
            'from_date' => $request->input('from_date'),
            'to_date'   => $request->input('to_date'),
            'per_page'  => $request->input('per_page', 15),
        ];

        $filters['type'] = TransactionType::WITHDRAWAL;

        $withdrawals = $this->walletService->getTransactionHistory($customer->id, $filters);

        return response()->json([
            'success'    => true,
            'data'       => $withdrawals->items(),
            'pagination' => [
                'current_page' => $withdrawals->currentPage(),
                'last_page'    => $withdrawals->lastPage(),
                'per_page'     => $withdrawals->perPage(),
                'total'        => $withdrawals->total(),
                'from'         => $withdrawals->firstItem(),
                'to'           => $withdrawals->lastItem(),
            ],
        ]);
    }

    public function store(CreateWithdrawalRequest $request): JsonResponse
    {
        $customer = $this->customerSessionService->getCustomerOrFail();
        $currency = strtoupper($request->input('currency'));

        try {
            $this->limitValidationService->validateLimits(
                'withdraw',
                $customer->id,
                (float) $request->input('amount'),
                $currency,
                $request->input('method')
            );

            if (! $this->balanceService->hasSufficientBalance($customer->id, $currency, $request->input('amount'))) {
                return response()->json([
                    'success' => false,
                    'message' => 'Insufficient balance',
                ], 400);
            }

            $withdrawal = $this->walletService->createWithdrawal([
                'customer_id' => $customer->id,
                'method'      => $request->input('method'),
                'currency'    => $currency,
                'amount'      => $request->input('amount'),
                'description' => $request->input('description'),
                'metadata'    => [
                    'account_details' => $request->input('account_details'),
                    'ip_address'      => $request->ip(),
                    'user_agent'      => $request->userAgent(),
                ],
            ]);

            $this->permissionService->logPermissionCheck(
                'withdraw',
                $customer->id,
                $request->input('amount'),
                $currency,
                true
            );

            return response()->json([
                'success' => true,
                'message' => 'Withdrawal request created successfully',
                'data'    => [
                    'id'               => $withdrawal->id,
                    'reference'        => $withdrawal->reference,
                    'type'             => $withdrawal->type->value,
                    'status'           => $withdrawal->status->value,
                    'method'           => $withdrawal->method,
                    'currency'         => $withdrawal->currency,
                    'amount'           => $withdrawal->amount,
                    'formatted_amount' => $withdrawal->getFormattedAmount(),
                    'description'      => $withdrawal->description,
                    'created_at'       => $withdrawal->created_at->toISOString(),
                ],
            ]);

        } catch (WalletOperationException $exception) {
            $this->permissionService->logPermissionCheck(
                'withdraw',
                $customer->id,
                $request->input('amount'),
                $currency,
                false,
                $exception->getErrors()
            );

            return response()->json([
                'success' => false,
                'message' => $exception->getMessage(),
                'errors'  => $exception->getErrors(),
                'context' => $exception->getContext(),
            ], 403);

        } catch (Exception $exception) {
            return response()->json([
                'success' => false,
                'message' => $exception->getMessage(),
            ], 400);
        }
    }

    public function methods(Request $request): JsonResponse
    {
        $customer = $this->customerSessionService->getCustomerOrFail();
        $currency = $request->input('currency');

        $methods = $this->walletService->getAvailablePaymentMethods($customer->id, 'withdraw', $currency);

        return response()->json([
            'success' => true,
            'data'    => $methods,
        ]);
    }

    public function show(WalletTransaction $transaction): JsonResponse
    {
        $customer = $this->customerSessionService->getCustomerOrFail();

        if ($transaction->customer_id !== $customer->id || $transaction->type !== TransactionType::WITHDRAWAL) {
            return response()->json([
                'success' => false,
                'message' => 'Withdrawal not found',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data'    => [
                'id'                      => $transaction->id,
                'reference'               => $transaction->reference,
                'type'                    => $transaction->type->value,
                'status'                  => $transaction->status->value,
                'method'                  => $transaction->method,
                'currency'                => $transaction->currency,
                'amount'                  => $transaction->amount,
                'fee'                     => $transaction->fee,
                'total_amount'            => $transaction->total_amount,
                'formatted_amount'        => $transaction->getFormattedAmount(),
                'formatted_fee'           => $transaction->getFormattedFee(),
                'formatted_total_amount'  => $transaction->getFormattedTotalAmount(),
                'description'             => $transaction->description,
                'metadata'                => $transaction->metadata,
                'external_reference'      => $transaction->external_reference,
                'external_transaction_id' => $transaction->external_transaction_id,
                'processed_at'            => $transaction->processed_at?->toISOString(),
                'failed_at'               => $transaction->failed_at?->toISOString(),
                'failure_reason'          => $transaction->failure_reason,
                'created_at'              => $transaction->created_at->toISOString(),
                'updated_at'              => $transaction->updated_at->toISOString(),
                'age'                     => $transaction->getAge(),
            ],
        ]);
    }
}
