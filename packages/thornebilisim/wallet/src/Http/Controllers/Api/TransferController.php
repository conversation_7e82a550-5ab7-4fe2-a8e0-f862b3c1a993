<?php

namespace Thorne\Wallet\Http\Controllers\Api;

use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Thorne\Wallet\Exceptions\WalletOperationException;
use Thorne\Wallet\Http\Controllers\Controller;
use Thorne\Wallet\Http\Requests\CreateTransferRequest;
use Thorne\Wallet\Models\WalletAccount;
use Thorne\Wallet\Models\WalletTransfer;
use Thorne\Wallet\Services\BalanceService;
use Thorne\Wallet\Services\CustomerSessionService;
use Thorne\Wallet\Services\LuhnService;
use Thorne\Wallet\Services\PermissionService;
use Thorne\Wallet\Services\TransferService;
use Thorne\Wallet\Services\LimitValidationService;

class TransferController extends Controller
{
    public function __construct(
        protected TransferService $transferService,
        protected BalanceService $balanceService,
        protected LuhnService $luhnService,
        protected PermissionService $permissionService,
        protected CustomerSessionService $customerSessionService,
        protected LimitValidationService $limitValidationService
    ) {}

    public function index(Request $request): JsonResponse
    {
        $customer = $this->customerSessionService->getCustomerOrFail();

        $filters = [
            'currency'  => $request->input('currency'),
            'status'    => $request->input('status'),
            'direction' => $request->input('direction'),
            'from_date' => $request->input('from_date'),
            'to_date'   => $request->input('to_date'),
            'per_page'  => $request->input('per_page', 15),
        ];

        $transfers = $this->transferService->getTransferHistory($customer->id, $filters);

        return response()->json([
            'success'    => true,
            'data'       => $transfers->items(),
            'pagination' => [
                'current_page' => $transfers->currentPage(),
                'last_page'    => $transfers->lastPage(),
                'per_page'     => $transfers->perPage(),
                'total'        => $transfers->total(),
                'from'         => $transfers->firstItem(),
                'to'           => $transfers->lastItem(),
            ],
        ]);
    }

    public function store(CreateTransferRequest $request): JsonResponse
    {
        $customer = $this->customerSessionService->getCustomerOrFail();

        try {
            $fromAccount = WalletAccount::where('account_number', $request->input('from_account_number'))
                ->where('customer_id', $customer->id)
                ->active()
                ->first();

            if (! $fromAccount) {
                return response()->json([
                    'success' => false,
                    'message' => 'From account not found or not owned by customer',
                ], 400);
            }

            $this->limitValidationService->validateLimits(
                'transfer',
                $customer->id,
                $request->input('amount'),
                $fromAccount->currency
            );

            $toAccount = WalletAccount::findByAccountNumber($request->input('to_account_number'));
            if (! $toAccount) {
                return response()->json([
                    'success' => false,
                    'message' => 'To account not found',
                ], 400);
            }

            if ($request->input('from_account_number') === $request->input('to_account_number')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot transfer to the same account',
                ], 400);
            }

            if (! $this->balanceService->hasSufficientBalance($customer->id, $fromAccount->currency, $request->input('amount'))) {
                return response()->json([
                    'success' => false,
                    'message' => 'Insufficient balance',
                ], 400);
            }

            $transfer = $this->transferService->createTransfer([
                'from_account_number' => $request->input('from_account_number'),
                'to_account_number'   => $request->input('to_account_number'),
                'amount'              => $request->input('amount'),
                'description'         => $request->input('description'),
                'metadata'            => [
                    'ip_address' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                ],
            ]);

            $this->permissionService->logPermissionCheck(
                'transfer',
                $customer->id,
                $request->input('amount'),
                $fromAccount->currency,
                true
            );

            return response()->json([
                'success' => true,
                'message' => 'Transfer created successfully',
                'data'    => [
                    'id'                     => $transfer->id,
                    'reference'              => $transfer->reference,
                    'from_customer_id'       => $transfer->from_customer_id,
                    'to_customer_id'         => $transfer->to_customer_id,
                    'from_account_number'    => $transfer->from_account_number,
                    'to_account_number'      => $transfer->to_account_number,
                    'currency'               => $transfer->currency,
                    'amount'                 => $transfer->amount,
                    'fee'                    => $transfer->fee,
                    'total_amount'           => $transfer->total_amount,
                    'status'                 => $transfer->status->value,
                    'formatted_amount'       => $transfer->getFormattedAmount(),
                    'formatted_fee'          => $transfer->getFormattedFee(),
                    'formatted_total_amount' => $transfer->getFormattedTotalAmount(),
                    'description'            => $transfer->description,
                    'created_at'             => $transfer->created_at->toISOString(),
                ],
            ]);

        } catch (WalletOperationException $exception) {
            $this->permissionService->logPermissionCheck(
                'transfer',
                $customer->id,
                $request->input('amount'),
                $fromAccount->currency ?? 'EUR',
                false,
                $exception->getErrors()
            );

            return response()->json([
                'success' => false,
                'message' => $exception->getMessage(),
                'errors'  => $exception->getErrors(),
                'context' => $exception->getContext(),
            ], 403);

        } catch (Exception $exception) {
            return response()->json([
                'success' => false,
                'message' => $exception->getMessage(),
            ], 400);
        }
    }

    public function validateAccount(Request $request): JsonResponse
    {
        $accountNumber = $request->input('account_number');
        $customer      = $this->customerSessionService->getCustomerOrFail();

        if (! $accountNumber || strlen($accountNumber) !== 11) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid account number format',
            ], 400);
        }

        if (! $this->luhnService->validateAccountNumber($accountNumber)) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid account number (Luhn validation failed)',
            ], 400);
        }

        $account = WalletAccount::findByAccountNumber($accountNumber);
        if (! $account) {
            return response()->json([
                'success' => false,
                'message' => 'Account not found',
            ], 404);
        }

        if ($account->customer_id === $customer->id) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot transfer to your own account',
            ], 400);
        }

        return response()->json([
            'success' => true,
            'message' => 'Account is valid',
            'data'    => [
                'account_number'           => $account->account_number,
                'formatted_account_number' => $account->getFormattedAccountNumber(),
                'currency'                 => $account->currency,
                'customer_name'            => $account->customer->first_name.' '.$account->customer->last_name,
                'is_active'                => $account->is_active,
            ],
        ]);
    }

    public function show(WalletTransfer $transfer): JsonResponse
    {
        $customer = $this->customerSessionService->getCustomerOrFail();

        if ($transfer->from_customer_id !== $customer->id && $transfer->to_customer_id !== $customer->id) {
            return response()->json([
                'success' => false,
                'message' => 'Transfer not found',
            ], 404);
        }

        $transfer->load(['fromCustomer', 'toCustomer', 'fromAccount', 'toAccount']);

        return response()->json([
            'success' => true,
            'data'    => [
                'id'                     => $transfer->id,
                'reference'              => $transfer->reference,
                'from_customer_id'       => $transfer->from_customer_id,
                'to_customer_id'         => $transfer->to_customer_id,
                'from_account_number'    => $transfer->from_account_number,
                'to_account_number'      => $transfer->to_account_number,
                'currency'               => $transfer->currency,
                'amount'                 => $transfer->amount,
                'fee'                    => $transfer->fee,
                'total_amount'           => $transfer->total_amount,
                'status'                 => $transfer->status->value,
                'formatted_amount'       => $transfer->getFormattedAmount(),
                'formatted_fee'          => $transfer->getFormattedFee(),
                'formatted_total_amount' => $transfer->getFormattedTotalAmount(),
                'description'            => $transfer->description,
                'metadata'               => $transfer->metadata,
                'processed_at'           => $transfer->processed_at?->toISOString(),
                'failed_at'              => $transfer->failed_at?->toISOString(),
                'failure_reason'         => $transfer->failure_reason,
                'created_at'             => $transfer->created_at->toISOString(),
                'updated_at'             => $transfer->updated_at->toISOString(),
                'age'                    => $transfer->getAge(),
                'from_customer'          => [
                    'id'    => $transfer->fromCustomer->id,
                    'name'  => $transfer->fromCustomer->first_name.' '.$transfer->fromCustomer->last_name,
                    'email' => $transfer->fromCustomer->email,
                ],
                'to_customer' => [
                    'id'    => $transfer->toCustomer->id,
                    'name'  => $transfer->toCustomer->first_name.' '.$transfer->toCustomer->last_name,
                    'email' => $transfer->toCustomer->email,
                ],
            ],
        ]);
    }

    public function cancel(WalletTransfer $transfer, Request $request): JsonResponse
    {
        $customer = $this->customerSessionService->getCustomerOrFail();

        if ($transfer->from_customer_id !== $customer->id) {
            return response()->json([
                'success' => false,
                'message' => 'You can only cancel your own outgoing transfers',
            ], 403);
        }

        if (! $transfer->isPending()) {
            return response()->json([
                'success' => false,
                'message' => 'Only pending transfers can be cancelled',
            ], 400);
        }

        try {
            $reason = $request->input('reason', 'Cancelled by customer');
            $this->transferService->cancelTransfer($transfer, $reason);

            return response()->json([
                'success' => true,
                'message' => 'Transfer cancelled successfully',
                'data'    => [
                    'id'             => $transfer->id,
                    'reference'      => $transfer->reference,
                    'status'         => $transfer->fresh()->status->value,
                    'failure_reason' => $reason,
                ],
            ]);

        } catch (Exception $exception) {
            return response()->json([
                'success' => false,
                'message' => $exception->getMessage(),
            ], 400);
        }
    }
}
