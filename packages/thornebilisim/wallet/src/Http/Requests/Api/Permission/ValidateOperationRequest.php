<?php

namespace Thorne\Wallet\Http\Requests\Api\Permission;

use Illuminate\Foundation\Http\FormRequest;
use Thorne\Wallet\Http\Requests\Api\Traits\HasCurrencyValidation;
use Thorne\Wallet\Http\Requests\Api\Rules\ValidOperation;
use Thorne\Wallet\Http\Requests\Api\Rules\SupportedPaymentMethod;

class ValidateOperationRequest extends FormRequest
{
    use HasCurrencyValidation;

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return array_merge([
            'operation' => ['required', 'string', new ValidOperation()],
            'amount'    => ['required', 'numeric', 'min:0.01'],
            'method'    => ['nullable', 'string', new SupportedPaymentMethod($this->input('operation'), $this->input('currency'))],
        ], $this->getCurrencyRules(true));
    }

    public function messages(): array
    {
        return array_merge([
            'operation.required' => __('wallet::validation.operation.required'),
            'operation.string'   => __('wallet::validation.operation.string'),
            'amount.required'    => __('wallet::validation.amount.required'),
            'amount.numeric'     => __('wallet::validation.amount.numeric'),
            'amount.min'         => __('wallet::validation.amount.min'),
            'method.string'      => __('wallet::validation.method.string'),
        ], $this->getCurrencyMessages());
    }
}
