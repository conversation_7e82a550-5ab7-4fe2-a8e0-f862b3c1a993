<?php

namespace Thorne\Wallet\Services;

use Illuminate\Support\Facades\Log;

class PermissionService
{
    public function isOperationEnabled(string $operation): bool
    {
        return wallet_config("permissions.{$operation}.enabled", true);
    }

    public function isKycRequired(string $operation): bool
    {
        return wallet_config("permissions.{$operation}.require_kyc", true);
    }

    public function isApprovalRequired(string $operation): bool
    {
        return wallet_config("permissions.{$operation}.require_approval", false);
    }

    public function isDailyLimitEnabled(string $operation): bool
    {
        return wallet_config("permissions.{$operation}.enable_daily_limit", true);
    }

    public function isMonthlyLimitEnabled(string $operation): bool
    {
        return wallet_config("permissions.{$operation}.enable_monthly_limit", true);
    }

    public function areAmountLimitsEnabled(string $operation): bool
    {
        return wallet_config("permissions.{$operation}.enable_amount_limits", true);
    }

    public function getDailyLimit(string $operation): float
    {
        return (float) wallet_config("permissions.{$operation}.daily_limit", 0);
    }

    public function getMonthlyLimit(string $operation): float
    {
        return (float) wallet_config("permissions.{$operation}.monthly_limit", 0);
    }

    public function getMinAmount(string $operation): float
    {
        return (float) wallet_config("permissions.{$operation}.min_amount", 0);
    }

    public function getMaxAmount(string $operation): float
    {
        return (float) wallet_config("permissions.{$operation}.max_amount", 0);
    }

    public function getOperationLimits(string $operation): array
    {
        return [
            'enabled'          => $this->isOperationEnabled($operation),
            'require_kyc'      => $this->isKycRequired($operation),
            'require_approval' => $this->isApprovalRequired($operation),
            'daily_limit'      => [
                'enabled' => $this->isDailyLimitEnabled($operation),
                'amount'  => $this->getDailyLimit($operation),
            ],
            'monthly_limit' => [
                'enabled' => $this->isMonthlyLimitEnabled($operation),
                'amount'  => $this->getMonthlyLimit($operation),
            ],
            'amount_limits' => [
                'enabled' => $this->areAmountLimitsEnabled($operation),
                'min'     => $this->getMinAmount($operation),
                'max'     => $this->getMaxAmount($operation),
            ],
        ];
    }

    public function logPermissionCheck(string $operation, int $customerId, float $amount, string $currency, bool $allowed, array $errors = []): void
    {
        if (! wallet_config('logging.enabled', true)) {
            return;
        }

        Log::channel(wallet_config('logging.channel', 'wallet'))
            ->info('Permission check', [
                'operation'   => $operation,
                'customer_id' => $customerId,
                'amount'      => $amount,
                'currency'    => $currency,
                'allowed'     => $allowed,
                'errors'      => $errors,
                'timestamp'   => now()->toISOString(),
            ]);
    }
}
