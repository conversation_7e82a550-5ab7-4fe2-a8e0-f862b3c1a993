<?php

namespace Thorne\Wallet\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Thorne\Wallet\Http\Requests\Traits\HasCustomerContext;

class WebWithdrawRequest extends FormRequest
{
    use HasCustomerContext;

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'method' => [
                'required',
                'string',
                function ($attribute, $value, $fail) {
                    $methodConfig = wallet_payment_method_config($value);
                    if (empty($methodConfig) || ! $methodConfig['enabled'] || ! $methodConfig['can_withdraw']) {
                        $fail(__('wallet::app.errors.method_not_supported'));
                    }
                },
            ],
            'currency' => [
                'required',
                'string',
                'between:3,4',
                function ($attribute, $value, $fail) {
                    if (! wallet_is_supported_currency(strtoupper($value))) {
                        $fail(__('wallet::app.errors.unsupported_currency'));
                    }
                },
            ],
            'amount' => [
                'required',
                'numeric',
                'min:0.01',
            ],
            'account_details' => 'required|array',
            'description'     => 'nullable|string|max:255',
        ];
    }

    public function messages(): array
    {
        return [
            'method.required'             => __('wallet::app.errors.method_required'),
            'currency.required'           => __('wallet::app.errors.currency_required'),
            'currency.between'            => __('wallet::app.errors.currency_between'),
            'amount.required'             => __('wallet::app.errors.amount_required'),
            'amount.numeric'              => __('wallet::app.errors.amount_numeric'),
            'amount.min'                  => __('wallet::app.errors.amount_min'),
            'account_details.required'    => __('wallet::app.errors.account_details_required'),
            'account_details.array'       => __('wallet::app.errors.account_details_array'),
            'description.string'          => __('wallet::app.errors.description_string'),
            'description.max'             => __('wallet::app.errors.description_max'),
        ];
    }
}
