<?php

namespace Thorne\Wallet\Http\Requests\Api\Transfer;

use Illuminate\Foundation\Http\FormRequest;
use Thorne\Wallet\Http\Requests\Api\Rules\ValidAccountNumber;
use Thorne\Wallet\Http\Requests\Traits\HasCustomerContext;

class ValidateTransferAccountRequest extends FormRequest
{
    use HasCustomerContext;

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'account_number' => [
                'required',
                'string',
                new ValidAccountNumber(),
                function ($attribute, $value, $fail) {
                    // Check if not customer's own account
                    $customer = $this->getCustomer();
                    $account = \Thorne\Wallet\Models\WalletAccount::findByAccountNumber($value);
                    
                    if ($account && $account->customer_id === $customer->id) {
                        $fail(__('wallet::validation.account_number.same_customer'));
                    }
                },
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'account_number.required' => __('wallet::validation.account_number.required'),
            'account_number.string'   => __('wallet::validation.account_number.string'),
        ];
    }
}
