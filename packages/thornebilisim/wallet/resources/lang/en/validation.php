<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Wallet Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines contain the default validation messages
    | used by the wallet package form request classes.
    |
    */

    'currency' => [
        'required'       => 'Currency is required.',
        'invalid_length' => 'Currency code must be between :min and :max characters.',
        'unsupported'    => 'Currency :currency is not supported. Supported currencies: :supported',
    ],

    'payment_method' => [
        'not_found'                => 'Payment method :method not found.',
        'disabled'                 => 'Payment method :method is currently disabled.',
        'operation_not_supported'  => 'Payment method :method does not support :operation operations.',
        'currency_not_supported'   => 'Payment method :method does not support :currency currency. Supported currencies: :supported',
    ],

    'account_number' => [
        'required'        => 'Account number is required.',
        'string'          => 'Account number must be a string.',
        'invalid_format'  => 'Invalid account number format.',
        'not_found'       => 'Account not found.',
        'inactive'        => 'Account is inactive.',
        'same_customer'   => 'Cannot use your own account for this operation.',
    ],

    'balance' => [
        'insufficient'  => 'Insufficient balance for :currency :amount.',
        'check_failed'  => 'Unable to verify balance at this time.',
    ],

    'operation' => [
        'required' => 'Operation is required.',
        'string'   => 'Operation must be a string.',
        'invalid'  => 'Invalid operation :operation. Allowed operations: :allowed',
    ],

    'method' => [
        'required' => 'Payment method is required.',
        'string'   => 'Payment method must be a string.',
    ],

    'amount' => [
        'required' => 'Amount is required.',
        'numeric'  => 'Amount must be a number.',
        'min'      => 'Amount must be at least 0.01.',
    ],

    'description' => [
        'required' => 'Description is required.',
        'string'   => 'Description must be a string.',
        'max'      => 'Description cannot exceed 255 characters.',
    ],

    'account_id' => [
        'string' => 'Account ID must be a string.',
    ],

    'account_details' => [
        'required' => 'Account details are required.',
        'array'    => 'Account details must be an array.',
    ],

    'from_account_number' => [
        'required' => 'From account number is required.',
        'string'   => 'From account number must be a string.',
    ],

    'to_account_number' => [
        'required'  => 'To account number is required.',
        'string'    => 'To account number must be a string.',
        'different' => 'To account number must be different from from account number.',
    ],

    'from_account' => [
        'not_owned' => 'From account does not belong to you or is inactive.',
    ],

    'to_account' => [
        'same_customer' => 'Cannot transfer to your own account.',
    ],

    'status' => [
        'invalid' => 'Invalid status. Valid statuses: pending, completed, failed, cancelled.',
    ],

    'type' => [
        'invalid' => 'Invalid transaction type. Valid types: deposit, withdrawal, transfer_in, transfer_out.',
    ],

    'direction' => [
        'invalid' => 'Invalid direction. Valid directions: incoming, outgoing, all.',
    ],

    'sync' => [
        'boolean' => 'Sync parameter must be true or false.',
    ],

    'pagination' => [
        'per_page_integer' => 'Per page must be an integer.',
        'per_page_min'     => 'Per page must be at least 1.',
        'per_page_max'     => 'Per page cannot exceed 100.',
    ],

    'date_range' => [
        'from_date_invalid'        => 'From date is not a valid date.',
        'from_date_before_to_date' => 'From date must be before or equal to to date.',
        'to_date_invalid'          => 'To date is not a valid date.',
        'to_date_after_from_date'  => 'To date must be after or equal to from date.',
    ],

    'xss' => [
        'invalid_characters' => 'Field contains invalid characters that are not allowed for security reasons.',
    ],
];
