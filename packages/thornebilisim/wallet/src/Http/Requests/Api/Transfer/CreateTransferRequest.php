<?php

namespace Thorne\Wallet\Http\Requests\Api\Transfer;

use Illuminate\Foundation\Http\FormRequest;
use Thorne\Wallet\Http\Requests\Api\Traits\HasXssProtection;
use Thorne\Wallet\Http\Requests\Api\Rules\ValidAccountNumber;
use Thorne\Wallet\Http\Requests\Traits\HasCustomerContext;

class CreateTransferRequest extends FormRequest
{
    use HasXssProtection, HasCustomerContext;

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return array_merge([
            'from_account_number' => [
                'required',
                'string',
                new ValidAccountNumber(),
                function ($attribute, $value, $fail) {
                    // Check if account belongs to customer
                    $customer = $this->getCustomer();
                    $account = \Thorne\Wallet\Models\WalletAccount::where('account_number', $value)
                        ->where('customer_id', $customer->id)
                        ->active()
                        ->first();
                    
                    if (!$account) {
                        $fail(__('wallet::validation.from_account.not_owned'));
                    }
                },
            ],
            'to_account_number' => [
                'required',
                'string',
                'different:from_account_number',
                new ValidAccountNumber(),
                function ($attribute, $value, $fail) {
                    // Check if not transferring to own account
                    $customer = $this->getCustomer();
                    $account = \Thorne\Wallet\Models\WalletAccount::findByAccountNumber($value);
                    
                    if ($account && $account->customer_id === $customer->id) {
                        $fail(__('wallet::validation.to_account.same_customer'));
                    }
                },
            ],
            'amount' => [
                'required',
                'numeric',
                'min:0.01',
            ],
        ], $this->getDescriptionRules());
    }

    public function messages(): array
    {
        return array_merge([
            'from_account_number.required'  => __('wallet::validation.from_account_number.required'),
            'from_account_number.string'    => __('wallet::validation.from_account_number.string'),
            'to_account_number.required'    => __('wallet::validation.to_account_number.required'),
            'to_account_number.string'      => __('wallet::validation.to_account_number.string'),
            'to_account_number.different'   => __('wallet::validation.to_account_number.different'),
            'amount.required'               => __('wallet::validation.amount.required'),
            'amount.numeric'                => __('wallet::validation.amount.numeric'),
            'amount.min'                    => __('wallet::validation.amount.min'),
        ], $this->getXssProtectionMessages());
    }
}
