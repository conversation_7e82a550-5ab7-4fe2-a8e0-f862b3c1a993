<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Wallet Validation Language Lines (Turkish)
    |--------------------------------------------------------------------------
    |
    | The following language lines contain the default validation messages
    | used by the wallet package form request classes.
    |
    */

    'currency' => [
        'required'       => 'Para birimi gereklidir.',
        'invalid_length' => 'Para birimi kodu :min ile :max karakter arasında olmalıdır.',
        'unsupported'    => ':currency para birimi desteklenmiyor. Desteklenen para birimleri: :supported',
    ],

    'payment_method' => [
        'not_found'                => ':method ödeme yöntemi bulunamadı.',
        'disabled'                 => ':method ödeme yöntemi şu anda devre dışı.',
        'operation_not_supported'  => ':method ödeme yöntemi :operation işlemlerini desteklemiyor.',
        'currency_not_supported'   => ':method ödeme yöntemi :currency para birimini desteklemiyor. Desteklenen para birimleri: :supported',
    ],

    'account_number' => [
        'required'        => 'Hesap numarası gereklidir.',
        'string'          => 'Hesap numarası metin olmalıdır.',
        'invalid_format'  => 'Geçersiz hesap numarası formatı.',
        'not_found'       => 'Hesap bulunamadı.',
        'inactive'        => 'Hesap aktif değil.',
        'same_customer'   => 'Bu işlem için kendi hesabınızı kullanamazsınız.',
    ],

    'balance' => [
        'insufficient'  => ':currency :amount için yetersiz bakiye.',
        'check_failed'  => 'Bakiye şu anda doğrulanamıyor.',
    ],

    'operation' => [
        'required' => 'İşlem gereklidir.',
        'string'   => 'İşlem metin olmalıdır.',
        'invalid'  => 'Geçersiz işlem :operation. İzin verilen işlemler: :allowed',
    ],

    'method' => [
        'required' => 'Ödeme yöntemi gereklidir.',
        'string'   => 'Ödeme yöntemi metin olmalıdır.',
    ],

    'amount' => [
        'required' => 'Tutar gereklidir.',
        'numeric'  => 'Tutar sayı olmalıdır.',
        'min'      => 'Tutar en az 0.01 olmalıdır.',
    ],

    'description' => [
        'required' => 'Açıklama gereklidir.',
        'string'   => 'Açıklama metin olmalıdır.',
        'max'      => 'Açıklama 255 karakteri geçemez.',
    ],

    'account_id' => [
        'string' => 'Hesap ID metin olmalıdır.',
    ],

    'account_details' => [
        'required' => 'Hesap detayları gereklidir.',
        'array'    => 'Hesap detayları dizi olmalıdır.',
    ],

    'from_account_number' => [
        'required' => 'Gönderen hesap numarası gereklidir.',
        'string'   => 'Gönderen hesap numarası metin olmalıdır.',
    ],

    'to_account_number' => [
        'required'  => 'Alıcı hesap numarası gereklidir.',
        'string'    => 'Alıcı hesap numarası metin olmalıdır.',
        'different' => 'Alıcı hesap numarası gönderen hesap numarasından farklı olmalıdır.',
    ],

    'from_account' => [
        'not_owned' => 'Gönderen hesap size ait değil veya aktif değil.',
    ],

    'to_account' => [
        'same_customer' => 'Kendi hesabınıza transfer yapamazsınız.',
    ],

    'status' => [
        'invalid' => 'Geçersiz durum. Geçerli durumlar: pending, completed, failed, cancelled.',
    ],

    'type' => [
        'invalid' => 'Geçersiz işlem türü. Geçerli türler: deposit, withdrawal, transfer_in, transfer_out.',
    ],

    'direction' => [
        'invalid' => 'Geçersiz yön. Geçerli yönler: incoming, outgoing, all.',
    ],

    'sync' => [
        'boolean' => 'Sync parametresi true veya false olmalıdır.',
    ],

    'pagination' => [
        'per_page_integer' => 'Sayfa başına sayı tam sayı olmalıdır.',
        'per_page_min'     => 'Sayfa başına sayı en az 1 olmalıdır.',
        'per_page_max'     => 'Sayfa başına sayı 100\'ü geçemez.',
    ],

    'date_range' => [
        'from_date_invalid'        => 'Başlangıç tarihi geçerli bir tarih değil.',
        'from_date_before_to_date' => 'Başlangıç tarihi bitiş tarihinden önce veya eşit olmalıdır.',
        'to_date_invalid'          => 'Bitiş tarihi geçerli bir tarih değil.',
        'to_date_after_from_date'  => 'Bitiş tarihi başlangıç tarihinden sonra veya eşit olmalıdır.',
    ],

    'xss' => [
        'invalid_characters' => 'Alan güvenlik nedeniyle izin verilmeyen geçersiz karakterler içeriyor.',
    ],
];
