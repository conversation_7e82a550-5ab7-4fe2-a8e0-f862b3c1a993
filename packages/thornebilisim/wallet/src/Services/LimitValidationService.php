<?php

namespace Thorne\Wallet\Services;

use Carbon\Carbon;
use Thorne\Wallet\Enums\TransactionType;
use Thorne\Wallet\Exceptions\WalletException;
use Thorne\Wallet\Models\WalletTransaction;
use Webkul\Customer\Models\Customer;
use Exception;
use Thorne\Wallet\Exceptions\WalletOperationException;

class LimitValidationService
{
    public function __construct(
        protected PermissionService $permissionService
    ) {}

    public function validateLimits(string $operation, int $customerId, float $amount, string $currency, ?string $method = null): void
    {
        $errors = $this->validateOperation($operation, $customerId, $amount, $currency, $method);

        if (! empty($errors)) {
            throw new WalletOperationException(
                'Operation not allowed due to limit restrictions',
                'LIMIT_RESTRICTION',
                $errors
            );
        }
    }

    public function validateOperation(string $operation, int $customerId, float $amount, string $currency, ?string $method = null): array
    {
        $errors = [];

        $this->validateKyc($operation, $customerId);

        $amountErrors = $this->validateAmountLimits($operation, $customerId, $amount, $currency);
        if (! empty($amountErrors)) {
            $errors = array_merge($errors, $amountErrors);
        }

        $globalErrors = $this->validateGlobalLimits($operation, $customerId, $amount, $currency);
        if (! empty($globalErrors)) {
            $errors = array_merge($errors, $globalErrors);
        }

        if ($method) {
            $methodErrors = $this->validateMethodLimits($operation, $method, $amount, $currency);
            if (! empty($methodErrors)) {
                $errors = array_merge($errors, $methodErrors);
            }
        }

        return $errors;
    }

    public function getOperationLimits(string $operation): array
    {
        return [
            'daily'      => $this->permissionService->getDailyLimit($operation),
            'monthly'    => $this->permissionService->getMonthlyLimit($operation),
            'min_amount' => $this->permissionService->getMinAmount($operation),
            'max_amount' => $this->permissionService->getMaxAmount($operation),
        ];
    }

    public function getRemainingLimits(int $customerId, string $operation, string $currency): array
    {
        $dailyLimit   = $this->permissionService->getDailyLimit($operation);
        $monthlyLimit = $this->permissionService->getMonthlyLimit($operation);

        $dailyUsage   = $this->getDailyUsage($customerId, $operation, $currency);
        $monthlyUsage = $this->getMonthlyUsage($customerId, $operation, $currency);

        return [
            'daily'   => [
                'limit'   => $dailyLimit,
                'used'    => $dailyUsage,
                'remaining' => $dailyLimit - $dailyUsage,
            ],
            'monthly' => [
                'limit'   => $monthlyLimit,
                'used'    => $monthlyUsage,
                'remaining' => $monthlyLimit - $monthlyUsage,
            ],
        ];
    }

    public function getUsageStatistics(int $customerId, string $currency): array
    {
        $operations = ['deposit', 'withdraw', 'transfer'];
        $stats = [];

        foreach ($operations as $operation) {
            $dailyUsage = $this->getDailyUsage($customerId, $operation, $currency);
            $monthlyUsage = $this->getMonthlyUsage($customerId, $operation, $currency);
            $dailyLimit = $this->permissionService->getDailyLimit($operation);
            $monthlyLimit = $this->permissionService->getMonthlyLimit($operation);
            $minAmount = $this->permissionService->getMinAmount($operation);
            $maxAmount = $this->permissionService->getMaxAmount($operation);

            $stats[$operation] = [
                'daily' => [
                    'used' => $dailyUsage,
                    'limit' => $dailyLimit,
                    'remaining' => $dailyLimit - $dailyUsage,
                    'percentage' => $dailyLimit > 0 ? ($dailyUsage / $dailyLimit) * 100 : 0,
                    'enabled' => $this->permissionService->isDailyLimitEnabled($operation),
                ],
                'monthly' => [
                    'used' => $monthlyUsage,
                    'limit' => $monthlyLimit,
                    'remaining' => $monthlyLimit - $monthlyUsage,
                    'percentage' => $monthlyLimit > 0 ? ($monthlyUsage / $monthlyLimit) * 100 : 0,
                    'enabled' => $this->permissionService->isMonthlyLimitEnabled($operation),
                ],
                'amount_limits' => [
                    'min' => $minAmount,
                    'max' => $maxAmount,
                    'enabled' => $this->permissionService->areAmountLimitsEnabled($operation),
                ],
                'settings' => [
                    'enabled' => $this->permissionService->isOperationEnabled($operation),
                    'require_kyc' => $this->permissionService->isKycRequired($operation),
                    'require_approval' => $this->permissionService->isApprovalRequired($operation),
                ],
            ];
        }

        return $stats;
    }

    public function getUsageStatisticsForAllCurrencies(int $customerId): array
    {
        $currencies = wallet_config('currencies', []);
        $stats = [];

        foreach ($currencies as $code => $config) {
            if (! ($config['enabled'] ?? false)) {
                continue;
            }

            $stats[$code] = $this->getUsageStatistics($customerId, $code);
        }

        return $stats;
    }

    public function getOperationSummary(): array
    {
        $operations = ['deposit', 'withdraw', 'transfer'];
        $summary = [];

        foreach ($operations as $operation) {
            $summary[$operation] = [
                'enabled'          => $this->permissionService->isOperationEnabled($operation),
                'require_kyc'      => $this->permissionService->isKycRequired($operation),
                'require_approval' => $this->permissionService->isApprovalRequired($operation),
                'limits_enabled'   => [
                    'daily'   => $this->permissionService->isDailyLimitEnabled($operation),
                    'monthly' => $this->permissionService->isMonthlyLimitEnabled($operation),
                    'amount'  => $this->permissionService->areAmountLimitsEnabled($operation),
                ],
                'limits' => $this->getOperationLimits($operation),
            ];
        }

        return $summary;
    }

    public function validateAmountLimits(string $operation, int $customerId, float $amount, string $currency): void
    {
        $permissions = wallet_config("permissions.{$operation}", []);

        if (! $permissions['enabled']) {
            throw WalletOperationException::operationDisabled($operation);
        }

        $minAmount = $permissions['min_amount'] ?? '0.00000000';
        if (bccomp($amount, $minAmount, 8) < 0) {
            throw WalletOperationException::amountBelowMinimum($amount, (float) $minAmount, $currency);
        }

        $maxAmount = $permissions['max_amount'] ?? null;
        if ($maxAmount && bccomp($amount, $maxAmount, 8) > 0) {
            throw WalletOperationException::amountAboveMaximum($amount, (float) $maxAmount, $currency);
        }
    }

    protected function validateGlobalLimits(string $operation, int $customerId, float $amount, string $currency): array
    {
        $errors = [];

        if ($this->permissionService->isDailyLimitEnabled($operation)) {
            $dailyLimit = $this->permissionService->getDailyLimit($operation);
            $dailyUsage = $this->getDailyUsage($customerId, $operation, $currency);

            if (($dailyUsage + $amount) > $dailyLimit) {
                $errors[] = "Operation would exceed daily limit ({$dailyLimit} {$currency})";
            }
        }

        if ($this->permissionService->isMonthlyLimitEnabled($operation)) {
            $monthlyLimit = $this->permissionService->getMonthlyLimit($operation);
            $monthlyUsage = $this->getMonthlyUsage($customerId, $operation, $currency);

            if (($monthlyUsage + $amount) > $monthlyLimit) {
                $errors[] = "Operation would exceed monthly limit ({$monthlyLimit} {$currency})";
            }
        }

        return $errors;
    }

    protected function validateMethodLimits(string $operation, string $method, float $amount, string $currency): array
    {
        $errors = [];
        $methodConfig = wallet_config("payment_methods.{$method}", []);

        if (empty($methodConfig)) {
            $errors[] = "Invalid payment method: {$method}";
            return $errors;
        }

        if (! $methodConfig['enabled']) {
            $errors[] = "Payment method {$method} is not enabled";
        }

        if (! in_array($currency, $methodConfig['supported_currencies'])) {
            $errors[] = "Currency {$currency} is not supported for payment method {$method}";
        }

        $canPerformOperation = match ($operation) {
            'deposit'  => $methodConfig['can_deposit'],
            'withdraw' => $methodConfig['can_withdraw'],
            default    => false,
        };

        if (! $canPerformOperation) {
            $errors[] = "Operation {$operation} is not supported for payment method {$method}";
        }

        if (isset($methodConfig['limits'])) {
            $methodLimits = $methodConfig['limits'];

            if (isset($methodLimits['min_amount']) && $amount < $methodLimits['min_amount']) {
                $errors[] = "Amount is below minimum limit for payment method {$method}";
            }

            if (isset($methodLimits['max_amount']) && $amount > $methodLimits['max_amount']) {
                $errors[] = "Amount exceeds maximum limit for payment method {$method}";
            }
        }

        return $errors;
    }

    protected function validateOperationEnabled(string $operation): void
    {
        if (! $this->permissionService->isOperationEnabled($operation)) {
            throw new WalletException(
                ucfirst($operation).' operations are currently disabled',
                strtoupper($operation).'_DISABLED'
            );
        }
    }

    protected function validateKyc(string $operation, int $customerId): void
    {
        if ($this->permissionService->isKycRequired($operation)) {
            $customer = Customer::find($customerId);
            if (! $customer || ! $this->isCustomerKycVerified($customer)) {
                throw new WalletException(
                    "KYC verification is required for {$operation} operations",
                    'KYC_REQUIRED'
                );
            }
        }
    }

    protected function validateDailyLimits(string $operation, int $customerId, float $amount, string $currency): void
    {
        if (! $this->permissionService->isDailyLimitEnabled($operation)) {
            return;
        }

        $dailyLimit = $this->permissionService->getDailyLimit($operation);
        if ($dailyLimit <= 0) {
            return;
        }

        $dailyUsage = $this->getDailyUsage($customerId, $operation, $currency);
        if (($dailyUsage + $amount) > $dailyLimit) {
            $remaining = max(0, $dailyLimit - $dailyUsage);
            throw new WalletException(
                "Daily limit exceeded. Remaining: {$remaining} {$currency}",
                'DAILY_LIMIT_EXCEEDED',
                [
                    'operation'    => $operation,
                    'amount'       => $amount,
                    'daily_usage'  => $dailyUsage,
                    'daily_limit'  => $dailyLimit,
                    'remaining'    => $remaining,
                    'currency'     => $currency,
                ]
            );
        }
    }

    protected function validateMonthlyLimits(string $operation, int $customerId, float $amount, string $currency): void
    {
        if (! $this->permissionService->isMonthlyLimitEnabled($operation)) {
            return;
        }

        $monthlyLimit = $this->permissionService->getMonthlyLimit($operation);
        if ($monthlyLimit <= 0) {
            return;
        }

        $monthlyUsage = $this->getMonthlyUsage($customerId, $operation, $currency);
        if (($monthlyUsage + $amount) > $monthlyLimit) {
            $remaining = max(0, $monthlyLimit - $monthlyUsage);
            throw new WalletException(
                "Monthly limit exceeded. Remaining: {$remaining} {$currency}",
                'MONTHLY_LIMIT_EXCEEDED',
                [
                    'operation'     => $operation,
                    'amount'        => $amount,
                    'monthly_usage' => $monthlyUsage,
                    'monthly_limit' => $monthlyLimit,
                    'remaining'     => $remaining,
                    'currency'      => $currency,
                ]
            );
        }
    }

    protected function getDailyUsage(int $customerId, string $operation, string $currency): float
    {
        $transactionType = $this->getTransactionTypeForOperation($operation);
        $startOfDay      = Carbon::today();
        $endOfDay        = Carbon::tomorrow();

        return (float) WalletTransaction::where('customer_id', $customerId)
            ->where('type', $transactionType)
            ->where('currency', $currency)
            ->where('status', 'completed')
            ->whereBetween('created_at', [$startOfDay, $endOfDay])
            ->sum('amount');
    }

    protected function getMonthlyUsage(int $customerId, string $operation, string $currency): float
    {
        $transactionType = $this->getTransactionTypeForOperation($operation);
        $startOfMonth    = Carbon::now()->startOfMonth();
        $endOfMonth      = Carbon::now()->endOfMonth();

        return (float) WalletTransaction::where('customer_id', $customerId)
            ->where('type', $transactionType)
            ->where('currency', $currency)
            ->where('status', 'completed')
            ->whereBetween('created_at', [$startOfMonth, $endOfMonth])
            ->sum('amount');
    }

    protected function isCustomerKycVerified(Customer $customer): bool
    {
        return ! empty($customer->email)      &&
               ! empty($customer->first_name) &&
               ! empty($customer->last_name);
    }

    protected function getTransactionTypeForOperation(string $operation): string
    {
        return match ($operation) {
            'deposit'  => TransactionType::DEPOSIT->value,
            'withdraw' => TransactionType::WITHDRAWAL->value,
            'transfer' => TransactionType::TRANSFER->value,
            default    => $operation,
        };
    }

    public function getEffectiveLimits(string $operation, string $paymentMethod, string $currency): array
    {
        $globalPermissions = wallet_config("permissions.{$operation}", []);
        $methodConfig      = wallet_payment_method_config($paymentMethod);

        $globalMin = (float) ($globalPermissions['min_amount'] ?? 0);
        $globalMax = (float) ($globalPermissions['max_amount'] ?? PHP_FLOAT_MAX);

        $methodMin = (float) ($methodConfig['min_amount'] ?? 0);
        $methodMax = (float) ($methodConfig['max_amount'] ?? PHP_FLOAT_MAX);

        $effectiveMin = max($globalMin, $methodMin);
        $effectiveMax = min($globalMax, $methodMax);

        return [
            'min_amount'    => $effectiveMin,
            'max_amount'    => $effectiveMax,
            'currency'      => $currency,
            'global_limits' => [
                'min' => $globalMin,
                'max' => $globalMax,
            ],
            'payment_method_limits' => [
                'min' => $methodMin,
                'max' => $methodMax,
            ],
            'most_restrictive' => [
                'min_source' => $effectiveMin === $globalMin ? 'global' : 'payment_method',
                'max_source' => $effectiveMax === $globalMax ? 'global' : 'payment_method',
            ],
        ];
    }
}
